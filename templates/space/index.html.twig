{% extends 'base.html.twig' %}

{% block title %}Spaces{% endblock %}

{% block content %}
    <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
        <h1 class="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-0">Espaces disponibles</h1>
        {% if is_host() %}
        <a href="{{ path('app_space_new') }}" class="w-full sm:w-auto px-4 sm:px-5 py-2.5 bg-green-600 text-white rounded-lg hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 transition-colors flex items-center justify-center font-medium shadow-md text-sm sm:text-base">
            <svg class="w-4 h-4 sm:w-5 sm:h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
            </svg>
            Ajouter un espace
        </a>
        {% endif %}
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6">
        {% for space in spaces %}
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md overflow-hidden">
                <div class="p-4 sm:p-5">
                    <h2 class="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2">{{ space.name }}</h2>
                    <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300 mb-4">{{ space.description|slice(0, 100) }}{% if space.description|length > 100 %}...{% endif %}</p>
                    <div class="flex flex-col sm:flex-row sm:justify-between sm:items-center space-y-2 sm:space-y-0">
                        <span class="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                            Hôte : {{ space.host.firstname }} {{ space.host.lastname }}
                        </span>
                        <a href="{{ path('app_space_show', {'id': space.id}) }}" class="w-full sm:w-auto px-3 sm:px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600 transition-colors shadow-sm text-center text-sm sm:text-base">
                            Détails
                        </a>
                    </div>
                </div>
            </div>
        {% else %}
            <div class="col-span-full text-center py-8">
                <p class="text-sm sm:text-base text-gray-700 dark:text-gray-300">Aucun espace disponible pour le moment.</p>
            </div>
        {% endfor %}
    </div>
{% endblock %}
